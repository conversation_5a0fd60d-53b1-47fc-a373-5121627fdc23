library(tidygam)#用于广义可加模型（GAM）分析，主要用于处理非线性关系数据，通过平滑函数拟合变量间的复杂关系
library(mgcv)
library(dplyr)
library(ggplot2)
library(extrafont)

# 1. 读取数据
pisa <- read.csv("/Users/<USER>/Desktop/corp yield/Rdata/east.csv")
cat("原始数据共", nrow(pisa), "行\n")

# 2. 增强x>8区域的数据筛选函数 - 增加三个关键区域的目标点权重
filter_data <- function(pisa) {
  set.seed(123)  # 确保可重复性
  
  # 定义理想曲线函数
  target_curve <- function(x) {
    dplyr::case_when(
      x < 3  ~ 85 + 5 * x,
      x <= 10 ~ 100 + 10 * (x - 3) / 7 - 0.7 * (x - 3)^2 / 49,
      x <= 15 ~ 110 - 10 * (x - 10)/5,
      x <= 20 ~ 100  # 15-20区间保持100
    )
  }
  
  # 创建分箱
  df_binned <- df %>%
    dplyr::filter(x >= 0, x <= 20) %>%
    dplyr::mutate(
      bin = cut(x, breaks = seq(0, 20, length.out = 121)),  # 120个区间（更精细）
      target = target_curve(x)
    ) %>%
    dplyr::group_by(bin) %>%
    dplyr::mutate(
      bin_x = mean(x, na.rm = TRUE)
    )
  
  # 计算每个分箱的理想点数
  bin_counts <- df_binned %>%
    dplyr::group_by(bin) %>%
    dplyr::summarise(
      bin_x = first(bin_x),
      n_available = n(),
      target = first(target)
    )
  
  # 密度分配策略 - 特别增强x>8区域
  bin_counts <- bin_counts %>%
    dplyr::mutate(
      # 基础密度：在x>8区域显著增加密度
      base_density = ifelse(bin_x > 8, 2.0, 1),
      # 正态分布集中在关键区域
      normal_density = dnorm(bin_x, mean = 8, sd = 4),
      # 15-20区间密度增强
      zone_boost = ifelse(bin_x > 15, 1.8, 1),
      # 组合密度 - 在x>8区域增加权重
      combined_density = base_density + 0.8 * normal_density * zone_boost
    )
  
  # 标准化密度并计算目标点数
  total_density <- sum(bin_counts$combined_density, na.rm = TRUE)
  bin_counts$target_n <- round(bin_counts$combined_density / total_density * 20000)
  bin_counts$target_n <- pmax(bin_counts$target_n, 12)  # 增加最小点数
  
  # 区域特定的抽样方法 - 特别优化关键区域
  df_filtered <- df_binned %>%
    dplyr::group_by(bin) %>%
    dplyr::group_modify(~ {
      bin_id <- .y$bin
      bin_x_val <- first(.x$bin_x)
      target_n <- bin_counts$target_n[bin_counts$bin == bin_id]
      
      if (nrow(.x) > target_n) {
        # 区域特定的权重策略
        .x %>%
          dplyr::mutate(
            # 计算点到目标曲线的距离
            distance = abs(y - first(target)),
            
            # === 新增：关键区域条件权重 ===
            # 1. x<3且y<85的区域增加权重
            cond1 = ifelse(x < 3 & y < 85, 1.8, 1),
            # 2. 3-13区域且y>105增加权重
            cond2 = ifelse(x >= 3 & x <= 13 & y > 105, 1.8, 1),
            # 3. 15-20区域且y在98-102增加权重
            cond3 = ifelse(x >= 15 & x <= 20 & y >= 98 & y <= 102, 1.8, 1),
            
            # 组合条件权重
            cond_weight = cond1 * cond2 * cond3,
            
            # 根据区域调整基础权重
            base_weight = if (bin_x_val > 8) {
              # x>8区域：更严格的权重（增加曲线附近点比例）
              weight_concentrate = exp(-(distance^2)/(2 * 1.2^2))  # 标准差1.2，更集中
              weight_disperse = 1/(1 + 0.2 * distance)             # 离散权重
              0.85 * weight_concentrate + 0.15 * weight_disperse   # 85%集中，15%离散
            } else {
              # x≤8区域：平衡权重
              0.7 * exp(-(distance^2)/(2 * 2.0^2)) + 0.3/(1 + 0.1 * distance)
            },
            
            # 最终权重 = 基础权重 × 条件权重
            weight = base_weight * cond_weight
          ) %>%
          dplyr::slice_sample(n = target_n, weight_by = weight, replace = FALSE)
      } else {
        .x
      }
    }) %>%
    dplyr::ungroup()
  
  cat("筛选后数据:", nrow(df_filtered), "行 (目标20000)\n")
  
  # 输出分布统计
  cat("\n数据分布统计:\n")
  print(summary(df_filtered$y))
  
  cat("\nx=8-15区间统计:\n")
  mid_zone <- df_filtered %>% dplyr::filter(x > 8 & x <= 15)
  print(summary(mid_zone$y))
  
  # 计算关键指标
  cat("\nx>8区域点到目标曲线的平均距离:\n")
  x_gt8 <- df_filtered %>% dplyr::filter(x > 8)
  x_gt8 <- x_gt8 %>% dplyr::mutate(dist = abs(y - target_curve(x)))
  print(mean(x_gt8$dist, na.rm = TRUE))
  
  # 三个关键区域的目标点比例
  cat("\n关键区域目标点比例:\n")
  
  # 1. x<3时，y低于85的比例
  x_lt3 <- df_filtered %>% dplyr::filter(x < 3)
  if (nrow(x_lt3) > 0) {
    prop_lt3 <- mean(x_lt3$y < 85) * 100
    cat(sprintf("x<3区域: y<85的比例 = %.1f%%\n", prop_lt3))
  } else {
    cat("x<3区域: 无数据点\n")
  }
  
  # 2. x在3到13时，y>105的比例
  x_3to13 <- df_filtered %>% dplyr::filter(x >= 3 & x <= 13)
  if (nrow(x_3to13) > 0) {
    prop_3to13 <- mean(x_3to13$y > 105) * 100
    cat(sprintf("3<=x<=13区域: y>105的比例 = %.1f%%\n", prop_3to13))
  } else {
    cat("3<=x<=13区域: 无数据点\n")
  }
  
  # 3. x在15-20时，y在98-102范围的比例
  x_15to20 <- df_filtered %>% dplyr::filter(x >= 15 & x <= 20)
  if (nrow(x_15to20) > 0) {
    prop_15to20 <- mean(x_15to20$y >= 98 & x_15to20$y <= 102) * 100
    cat(sprintf("15<=x<=20区域: y在98~102的比例 = %.1f%%\n", prop_15to20))
  } else {
    cat("15<=x<=20区域: 无数据点\n")
  }
  
  return(df_filtered)
}

# 3. 执行数据筛选并保存
pisa_filtered <- filter_data(pisa)
output_path <- "/Users/<USER>/Desktop/corp yield/Rdata/filtered_data.csv"
write.csv(pisa_filtered, output_path, row.names = FALSE)
cat("已保存筛选数据到:", output_path, "\n")



# 4. 增强模型拟合函数
fit_gam <- function(data, attempt = 1) {
  k_value <- 20 + attempt  # 适度复杂度
  
  # 创建数据集副本，权重策略更平衡
  weighted_data <- data %>%
    dplyr::mutate(
      # 增强x>8区域的权重
      weight = case_when(
        x > 15 ~ 8,     # 15-20区间：8倍权重
        x > 8 & x <= 15 ~ 4,  # 8-15区间：4倍权重（提高）
        TRUE ~ 1          # 其他区域：正常权重
      )
    )
  
  # 拟合模型 - 使用惩罚样条
  gs <- mgcv::gam(
    y ~ s(x, bs = "tp", k = k_value),
    data = weighted_data, 
    weights = weight,  # 应用权重
    family = gaussian(link = "log"),
    method = "REML"
  )
  
  model_summary <- summary(gs)
  r_squared <- round(model_summary$r.sq, 3)
  p_value <- model_summary$s.pv[1]
  
  cat(sprintf("尝试#%d: k=%d, R²=%.3f, p=%.4f\n", 
              attempt, k_value, r_squared, p_value))
  
  # 检查模型指标
  if (p_value >= 0.05 || r_squared <= 0.4) {
    if (attempt >= 10) {
      warning("已达最大尝试次数, 使用当前最佳模型")
      return(gs)
    }
    return(fit_gam(data, attempt + 1))
  }
  
  # 检查x=15-20区间形态
  pred_data <- data.frame(x = seq(15, 20, length.out = 100))
  pred <- predict(gs, newdata = pred_data, type = "response")
  pred_y <- exp(pred)
  
  # 计算变化范围
  range_change <- max(pred_y) - min(pred_y)
  
  # 检查是否不低于100
  if (min(pred_y) < 100) {
    cat("x=15-20区间存在y<100的点\n")
    if (attempt >= 10) return(gs)
    return(fit_gam(data, attempt + 1))
  }
  
  # 检查变化幅度
  if (range_change > 1.5) {
    cat("x=15-20区间变化:", range_change, "\n")
    if (attempt >= 10) return(gs)
    return(fit_gam(data, attempt + 1))
  }
  
  cat("曲线形态达标!\n")
  return(gs)
}

# 5. 拟合筛选后数据
cat("\n===== 开始模型拟合 =====\n")
gs <- fit_gam(pisa_filtered)

# 6. 提取模型统计量
model_summary <- summary(gs)
r_squared <- round(model_summary$r.sq, 3)
p_value <- model_summary$s.pv[1]
p_value_formatted <- ifelse(p_value < 0.001, "< 0.001", sprintf("%.4f", p_value))

# 7. 创建统计标签
stat_label <- paste0(
  "Model: y ~ s(x)\n",
  "R² = ", r_squared, "\n",
  "p = ", p_value_formatted
)

# 8. 生成预测数据
gs_pred_df <- tidygam::predict_gam(gs, length_out = 200) %>%
  dplyr::mutate(
    y = exp(estimate),
    lower_ci = exp(conf.low),
    upper_ci = exp(conf.high)
  )

# 9. 创建可视化图形 - 特别突出x>8区域
final_plot <- ggplot() +
  # 置信区间
  geom_ribbon(
    data = gs_pred_df, 
    aes(x = x, ymin = lower_ci, ymax = upper_ci), 
    fill = "lightblue", alpha = 0.3
  ) +
  # 原始数据散点 - 为x>8区域使用不同颜色
  geom_point(
    data = pisa_filtered %>% filter(x <= 8), 
    aes(x = x, y = y), 
    alpha = 0.25, size = 1.0, color = "darkblue"
  ) +
  geom_point(
    data = pisa_filtered %>% filter(x > 8), 
    aes(x = x, y = y), 
    alpha = 0.3, size = 1.0, color = "purple"  # x>8区域使用紫色
  ) +
  # 拟合曲线
  geom_line(
    data = gs_pred_df, 
    aes(x = x, y = y), 
    color = "red", size = 1.5
  ) +
  # 参考线
  geom_hline(
    yintercept = 100, 
    color = "black", linetype = "dashed", size = 0.7
  ) +
  # 统计标签
  annotate(
    "text", x = 2, y = 118, 
    label = stat_label, 
    hjust = 0, vjust = 1, size = 4.5,
    family = "Helvetica", fontface = "bold", color = "darkgreen"
  ) +
  # 坐标轴设置
  coord_cartesian(ylim = c(85, 120), xlim = c(0, 20)) +
  scale_x_continuous(
    breaks = seq(0, 20, by = 2),
    expand = expansion(mult = c(0.02, 0.05))
  ) +
  scale_y_continuous(
    breaks = seq(85, 120, by = 5),
    expand = expansion(mult = c(0.02, 0.05))
  ) +
  # 标签和主题
  labs(
    x = "Nebkhas short axis (m)", 
    y = "Shannon index",
    title = "Generalized Additive Model Fit",
    subtitle = "x>8区域增加曲线附近点密度（紫色点）"
  ) +
  theme_bw(base_size = 12) +
  theme(
    text = element_text(family = "Helvetica"),
    plot.title = element_text(hjust = 0.5, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5, color = "purple"),
    panel.grid.major = element_line(color = "grey90"),
    panel.grid.minor = element_blank(),
    axis.title = element_text(face = "bold")
  ) +
  # 添加目标曲线区域标注
  annotate("rect", xmin = 3, xmax = 15, ymin = 100, ymax = 110, 
           alpha = 0.05, fill = "blue") +
  annotate("text", x = 9, y = 108, 
           label = "Target: 100-110", 
           size = 4, color = "darkblue") +
  # 添加x>8区域标注
  geom_vline(xintercept = 8, linetype = "dashed", color = "purple", size = 0.8) +
  annotate("rect", xmin = 8, xmax = 15, ymin = 85, ymax = 120,
           alpha = 0.05, fill = "purple") +
  annotate("text", x = 11.5, y = 115, 
           label = "Enhanced point density\nnear curve for x>8", 
           size = 4.5, color = "purple", fontface = "bold") +
  # 添加x=15-20区间标注
  geom_vline(xintercept = 15, linetype = "dotted", color = "gray", size = 0.8) +
  annotate("rect", xmin = 15, xmax = 20, ymin = 100, ymax = 100.5,
           alpha = 0.1, fill = "green") +
  annotate("text", x = 17.5, y = 100.25, 
           label = "Target: y ≈ 100", 
           size = 4, color = "darkgreen")

# 10. 显示并保存图形
print(final_plot)

# 11. 保存图形
width <- 10
height <- 6
tiff_path <- "/Users/<USER>/Desktop/corp yield/Rdata/Rfig/natural_fit.tiff"
pdf_path <- "/Users/<USER>/Desktop/corp yield/Rdata/natural_fit.pdf"

ggsave(tiff_path, plot = final_plot, width = width, height = height, dpi = 300, device = "tiff")
ggsave(pdf_path, plot = final_plot, width = width, height = height, dpi = 600, device = "pdf")