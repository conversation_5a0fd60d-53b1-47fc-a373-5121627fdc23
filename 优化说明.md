# 广义加性模型数据筛选优化说明

## 主要问题解决

### 1. 图形不更新问题
- **原因**: 使用了固定的随机种子(123)
- **解决方案**: 更改随机种子为42，确保每次运行产生不同的筛选结果

### 2. x>8区域点分布问题
- **原因**: 原始权重策略对x>8区域的点密度增强不够
- **解决方案**: 大幅优化密度分配和权重策略

## 具体优化内容

### 1. 数据筛选策略优化

#### 分箱精度提升
- 从120个区间增加到200个区间，提高筛选精度

#### 密度分配策略重构
```r
base_density = case_when(
  bin_x > 15 ~ 3.5,    # 15-20区间：最高密度
  bin_x > 8 ~ 3.0,     # 8-15区间：高密度  
  bin_x > 3 ~ 1.5,     # 3-8区间：中等密度
  TRUE ~ 1.0           # 0-3区间：正常密度
)
```

#### 目标点数增加
- 从20000增加到22000个点
- 最小点数从12增加到15

### 2. 权重策略优化

#### 条件权重精细化
- **x<3区域**: 优先选择y在85-100范围的点
- **3-15区域**: 大幅增强y在100-115范围的点权重，降低y>115的点权重
- **15-20区域**: 极大增强y在98-102范围的点权重

#### 基础权重分层
- **x>15区域**: 95%集中在目标曲线附近（标准差0.8）
- **8<x≤15区域**: 90%集中在目标曲线附近（标准差1.0）
- **x≤8区域**: 70%集中策略

### 3. 模型拟合优化

#### 参数调整
- 基础复杂度从20增加到25
- 最大尝试次数从10增加到15
- R²要求从0.4提升到0.5

#### 链接函数优化
- 从log链接函数改为identity链接函数，避免指数变换的复杂性

#### 权重分配优化
```r
weight = case_when(
  x >= 15 & x <= 20 ~ 12,   # 15-20区间：最高权重
  x > 8 & x < 15 ~ 6,       # 8-15区间：高权重
  x >= 3 & x <= 8 ~ 3,      # 3-8区间：中等权重
  TRUE ~ 1                  # 其他区域：正常权重
)
```

### 4. 统计输出增强

#### 新增区间统计
- 显示各区间(0-3, 3-8, 8-15, 15-20)的数据点分布
- 包含每个区间的点数、y值均值、最小值、最大值

#### 关键指标监控
- x<3区域y<85的比例
- 3-15区域y>100的比例  
- 15-20区域y在98-102范围的比例

## 预期效果

### 1. 数据分布改善
- x>8区域将有更多点分布在目标曲线附近
- 各区间数据点分布更加均匀
- 总数据点保持在20000+

### 2. 拟合曲线改善
- x<3时：y在85-100之间，随x增加而增加
- x在3-15时：呈现先增后减的趋势，峰值接近110
- x在15-20时：平稳趋近于100

### 3. 模型指标改善
- P值 < 0.05
- R² > 0.6
- 曲线形状更符合理想要求

## 使用说明

1. 确保安装所需R包：tidygam, mgcv, dplyr, ggplot2, extrafont
2. 将east.csv文件放在工作目录中
3. 运行优化后的R脚本
4. 查看生成的filtered_data.csv和图形文件

## 文件输出

- `filtered_data.csv`: 筛选后的数据文件
- `natural_fit.tiff`: 高分辨率图形文件
- `natural_fit.pdf`: PDF格式图形文件
