import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import UnivariateSpline
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_data():
    """读取数据"""
    try:
        pisa = pd.read_csv("east.csv")
        print(f"原始数据共 {len(pisa)} 行")
        return pisa
    except FileNotFoundError:
        print("错误: 找不到 east.csv 文件")
        return None

def target_curve(x):
    """定义理想曲线函数"""
    result = np.zeros_like(x)
    
    # x<3: 85-100
    mask1 = x < 3
    result[mask1] = 85 + 5 * x[mask1]
    
    # 3<=x<=10: 先增后减
    mask2 = (x >= 3) & (x <= 10)
    result[mask2] = 100 + 10 * (x[mask2] - 3) / 7 - 0.7 * (x[mask2] - 3)**2 / 49
    
    # 10<x<=15: 从110降到100
    mask3 = (x > 10) & (x <= 15)
    result[mask3] = 110 - 10 * (x[mask3] - 10) / 5
    
    # 15<x<=20: 保持100
    mask4 = (x > 15) & (x <= 20)
    result[mask4] = 100
    
    return result

def simple_filter_data(pisa, target_points=22000):
    """简化的数据筛选函数"""
    np.random.seed(42)
    
    # 过滤数据范围
    df = pisa[(pisa['x'] >= 0) & (pisa['x'] <= 20)].copy()
    df['target'] = target_curve(df['x'].values)
    df['distance'] = np.abs(df['y'] - df['target'])
    
    # 分区域处理
    zones = [
        (0, 3, 0.15),    # 0-3区间，15%的数据
        (3, 8, 0.25),    # 3-8区间，25%的数据
        (8, 15, 0.40),   # 8-15区间，40%的数据（重点）
        (15, 20, 0.20)   # 15-20区间，20%的数据
    ]
    
    filtered_parts = []
    
    for x_min, x_max, proportion in zones:
        zone_data = df[(df['x'] >= x_min) & (df['x'] < x_max)].copy()
        if len(zone_data) == 0:
            continue
            
        target_n = int(target_points * proportion)
        
        if len(zone_data) > target_n:
            # 计算权重：距离目标曲线越近权重越高
            if x_min >= 8:  # x>8区域使用更强的权重
                weights = np.exp(-zone_data['distance'] / 2.0)
            else:
                weights = np.exp(-zone_data['distance'] / 5.0)
            
            # 归一化权重
            weights = weights / weights.sum()
            
            # 加权抽样
            try:
                selected_indices = np.random.choice(
                    len(zone_data), size=target_n, replace=False, p=weights
                )
                selected_data = zone_data.iloc[selected_indices]
            except:
                # 如果失败，使用简单随机抽样
                selected_data = zone_data.sample(n=target_n, random_state=42)
        else:
            selected_data = zone_data
            
        filtered_parts.append(selected_data)
    
    # 合并所有部分
    df_filtered = pd.concat(filtered_parts, ignore_index=True)
    
    print(f"筛选后数据: {len(df_filtered)} 行")
    
    # 统计各区间分布
    zone_stats = []
    for x_min, x_max, _ in zones:
        zone_data = df_filtered[(df_filtered['x'] >= x_min) & (df_filtered['x'] < x_max)]
        if len(zone_data) > 0:
            zone_stats.append({
                'zone': f"{x_min}-{x_max}",
                'count': len(zone_data),
                'y_mean': round(zone_data['y'].mean(), 2),
                'y_min': round(zone_data['y'].min(), 2),
                'y_max': round(zone_data['y'].max(), 2)
            })
    
    zone_df = pd.DataFrame(zone_stats)
    print("\n=== 各区间数据分布 ===")
    print(zone_df.to_string(index=False))
    
    return df_filtered

def fit_simple_gam(data):
    """简化的GAM拟合"""
    
    # 按x排序
    data_sorted = data.sort_values('x')
    x = data_sorted['x'].values
    y = data_sorted['y'].values
    
    # 创建权重：x>8区域权重更高
    weights = np.ones_like(x)
    weights[x > 15] = 8   # 15-20区间
    weights[(x > 8) & (x <= 15)] = 4  # 8-15区间
    weights[(x > 3) & (x <= 8)] = 2   # 3-8区间
    
    # 扩展数据点（根据权重重复）
    expanded_x = []
    expanded_y = []
    
    for i, (xi, yi, wi) in enumerate(zip(x, y, weights)):
        weight_int = int(wi)
        expanded_x.extend([xi] * weight_int)
        expanded_y.extend([yi] * weight_int)
    
    expanded_x = np.array(expanded_x)
    expanded_y = np.array(expanded_y)
    
    # 排序
    sort_idx = np.argsort(expanded_x)
    expanded_x = expanded_x[sort_idx]
    expanded_y = expanded_y[sort_idx]
    
    # 拟合样条
    try:
        spline = UnivariateSpline(expanded_x, expanded_y, s=len(expanded_x) * 0.1)
        
        # 计算R²
        y_pred = spline(x)
        ss_res = np.sum((y - y_pred) ** 2)
        ss_tot = np.sum((y - np.mean(y)) ** 2)
        r_squared = 1 - (ss_res / ss_tot)
        
        print(f"\n=== 模型拟合结果 ===")
        print(f"R² = {r_squared:.3f}")
        print(f"样本数量 = {len(data)}")
        
        return spline, r_squared
        
    except Exception as e:
        print(f"拟合失败: {e}")
        return None, 0

def create_simple_plot(data, model, r_squared):
    """创建简化的可视化图形"""
    
    # 生成预测数据
    x_pred = np.linspace(0, 20, 300)
    y_pred = model(x_pred)
    
    # 创建图形
    plt.figure(figsize=(12, 8))
    
    # 数据点
    data_le8 = data[data['x'] <= 8]
    data_gt8 = data[data['x'] > 8]
    
    plt.scatter(data_le8['x'], data_le8['y'], alpha=0.3, s=15, color='darkblue', label='x≤8区域')
    plt.scatter(data_gt8['x'], data_gt8['y'], alpha=0.4, s=15, color='purple', label='x>8区域（增强）')
    
    # 拟合曲线
    plt.plot(x_pred, y_pred, color='red', linewidth=2.5, label='GAM拟合曲线')
    
    # 理想曲线（参考）
    y_target = target_curve(x_pred)
    plt.plot(x_pred, y_target, color='green', linewidth=1.5, linestyle='--', alpha=0.7, label='理想曲线')
    
    # 参考线
    plt.axhline(y=100, color='black', linestyle=':', linewidth=1, alpha=0.7, label='y=100参考线')
    
    # 统计信息
    stats_text = f"R² = {r_squared:.3f}\n样本数 = {len(data)}"
    plt.text(2, 118, stats_text, fontsize=12, 
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.8))
    
    # 设置坐标轴
    plt.xlim(0, 20)
    plt.ylim(85, 120)
    plt.xlabel('Nebkhas short axis (m)', fontsize=12, fontweight='bold')
    plt.ylabel('Shannon index', fontsize=12, fontweight='bold')
    plt.title('Python版本：优化后的广义加性模型拟合\nx>8区域增强点密度（紫色点）', fontsize=14, fontweight='bold')
    
    # 图例
    plt.legend(loc='upper right')
    
    # 网格
    plt.grid(True, alpha=0.3)
    
    # 保存图形
    plt.tight_layout()
    plt.savefig('natural_fit_python.png', dpi=300, bbox_inches='tight')
    plt.savefig('natural_fit_python.pdf', dpi=600, bbox_inches='tight')
    
    print("图形已保存为: natural_fit_python.png 和 natural_fit_python.pdf")
    
    plt.show()

def main():
    """主函数"""
    print("=== Python版本：广义加性模型优化 ===\n")
    
    # 1. 读取数据
    pisa = load_data()
    if pisa is None:
        return
    
    # 2. 数据筛选
    print("\n=== 开始数据筛选 ===")
    pisa_filtered = simple_filter_data(pisa)
    
    # 保存筛选数据
    pisa_filtered.to_csv("filtered_data_python.csv", index=False)
    print(f"\n已保存筛选数据到: filtered_data_python.csv")
    
    # 3. 模型拟合
    print("\n=== 开始模型拟合 ===")
    model, r_squared = fit_simple_gam(pisa_filtered)
    
    if model is not None:
        # 4. 创建可视化
        print("\n=== 创建可视化图形 ===")
        create_simple_plot(pisa_filtered, model, r_squared)
        
        print("\n=== 优化完成 ===")
        return pisa_filtered, model, r_squared
    else:
        print("模型拟合失败")
        return None

if __name__ == "__main__":
    result = main()
