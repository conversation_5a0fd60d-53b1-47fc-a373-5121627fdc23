library(tidygam)
library(mgcv)
library(dplyr)
library(ggplot2)
library(extrafont)

# 1. 读取数据
pisa <- read.csv("east.csv")
cat("原始数据共", nrow(pisa), "行\n")

# 2. 优化数据筛选函数
filter_data_optimized <- function(pisa) {
  set.seed(42)  # 更改随机种子确保图形更新
  
  # 定义理想曲线函数
  target_curve <- function(x) {
    dplyr::case_when(
      x < 3  ~ 85 + 5 * x,                                    # x<3: 85-100
      x <= 10 ~ 100 + 10 * (x - 3) / 7 - 0.7 * (x - 3)^2 / 49,  # 3-10: 先增后减
      x <= 15 ~ 110 - 10 * (x - 10)/5,                       # 10-15: 从110降到100
      x <= 20 ~ 100                                           # 15-20: 保持100
    )
  }
  
  # 创建更精细的分箱
  df_binned <- pisa %>%
    dplyr::filter(x >= 0, x <= 20) %>%
    dplyr::mutate(
      bin = cut(x, breaks = seq(0, 20, length.out = 201)),  # 200个区间
      target = target_curve(x)
    ) %>%
    dplyr::group_by(bin) %>%
    dplyr::mutate(bin_x = mean(x, na.rm = TRUE))
  
  # 计算分箱统计
  bin_counts <- df_binned %>%
    dplyr::group_by(bin) %>%
    dplyr::summarise(
      bin_x = first(bin_x),
      n_available = n(),
      target = first(target),
      .groups = 'drop'
    )
  
  # 优化密度分配 - 重点增强x>8区域
  bin_counts <- bin_counts %>%
    dplyr::mutate(
      # 基础密度：x>8区域大幅增加
      base_density = case_when(
        bin_x > 15 ~ 3.5,    # 15-20区间：最高密度
        bin_x > 8 ~ 3.0,     # 8-15区间：高密度
        bin_x > 3 ~ 1.5,     # 3-8区间：中等密度
        TRUE ~ 1.0           # 0-3区间：正常密度
      ),
      # 正态分布集中在关键区域
      normal_density = dnorm(bin_x, mean = 12, sd = 3),
      # 15-20区间特别增强
      zone_boost = case_when(
        bin_x >= 15 & bin_x <= 20 ~ 2.5,
        bin_x >= 8 & bin_x < 15 ~ 2.0,
        TRUE ~ 1.0
      ),
      # 组合密度
      combined_density = base_density * (1 + normal_density) * zone_boost
    )
  
  # 计算目标点数
  total_density <- sum(bin_counts$combined_density, na.rm = TRUE)
  bin_counts$target_n <- round(bin_counts$combined_density / total_density * 22000)
  bin_counts$target_n <- pmax(bin_counts$target_n, 15)
  
  # 优化抽样策略
  df_filtered <- df_binned %>%
    dplyr::group_by(bin) %>%
    dplyr::group_modify(~ {
      bin_id <- .y$bin
      bin_x_val <- first(.x$bin_x)
      target_n <- bin_counts$target_n[bin_counts$bin == bin_id]
      
      if (nrow(.x) > target_n) {
        .x %>%
          dplyr::mutate(
            distance = abs(y - first(target)),
            
            # 优化条件权重
            cond1 = ifelse(x < 3 & y >= 85 & y <= 100, 2.0, 
                          ifelse(x < 3 & y < 85, 1.5, 1)),
            cond2 = ifelse(x >= 3 & x <= 15 & y > 100 & y <= 115, 2.5, 
                          ifelse(x >= 3 & x <= 15 & y > 115, 0.3, 1)),
            cond3 = ifelse(x >= 15 & x <= 20 & y >= 98 & y <= 102, 3.0, 
                          ifelse(x >= 15 & x <= 20 & (y < 98 | y > 102), 0.2, 1)),
            
            cond_weight = cond1 * cond2 * cond3,
            
            # 优化基础权重
            base_weight = case_when(
              bin_x_val > 15 ~ {
                weight_concentrate = exp(-(distance^2)/(2 * 0.8^2))
                weight_disperse = 1/(1 + 0.5 * distance)
                0.95 * weight_concentrate + 0.05 * weight_disperse
              },
              bin_x_val > 8 ~ {
                weight_concentrate = exp(-(distance^2)/(2 * 1.0^2))
                weight_disperse = 1/(1 + 0.3 * distance)
                0.90 * weight_concentrate + 0.10 * weight_disperse
              },
              TRUE ~ 0.7 * exp(-(distance^2)/(2 * 2.0^2)) + 0.3/(1 + 0.1 * distance)
            ),
            
            weight = base_weight * cond_weight
          ) %>%
          dplyr::slice_sample(n = target_n, weight_by = weight, replace = FALSE)
      } else {
        .x
      }
    }) %>%
    dplyr::ungroup()
  
  # 输出统计信息
  cat("筛选后数据:", nrow(df_filtered), "行\n")
  
  # 各区间统计
  zone_stats <- df_filtered %>%
    dplyr::mutate(
      zone = case_when(
        x < 3 ~ "0-3",
        x <= 8 ~ "3-8", 
        x <= 15 ~ "8-15",
        x <= 20 ~ "15-20",
        TRUE ~ ">20"
      )
    ) %>%
    dplyr::group_by(zone) %>%
    dplyr::summarise(
      count = n(),
      y_mean = round(mean(y), 2),
      y_min = round(min(y), 2),
      y_max = round(max(y), 2),
      .groups = 'drop'
    )
  
  cat("\n=== 各区间数据分布 ===\n")
  print(zone_stats)
  
  return(df_filtered)
}

# 3. 优化模型拟合函数
fit_gam_optimized <- function(data, attempt = 1) {
  k_value <- 25 + attempt
  
  # 优化权重策略
  weighted_data <- data %>%
    dplyr::mutate(
      weight = case_when(
        x >= 15 & x <= 20 ~ 12,   # 15-20区间：最高权重
        x > 8 & x < 15 ~ 6,       # 8-15区间：高权重
        x >= 3 & x <= 8 ~ 3,      # 3-8区间：中等权重
        TRUE ~ 1                  # 其他区域：正常权重
      )
    )
  
  # 拟合模型
  gs <- mgcv::gam(
    y ~ s(x, bs = "tp", k = k_value),
    data = weighted_data, 
    weights = weight,
    family = gaussian(link = "identity"),  # 使用identity链接函数
    method = "REML"
  )
  
  model_summary <- summary(gs)
  r_squared <- round(model_summary$r.sq, 3)
  p_value <- model_summary$s.pv[1]
  
  cat(sprintf("尝试#%d: k=%d, R²=%.3f, p=%.4f\n", 
              attempt, k_value, r_squared, p_value))
  
  # 检查模型指标
  if (p_value >= 0.05 || r_squared <= 0.5) {
    if (attempt >= 15) {
      warning("已达最大尝试次数, 使用当前最佳模型")
      return(gs)
    }
    return(fit_gam_optimized(data, attempt + 1))
  }
  
  return(gs)
}

# 4. 执行优化流程
cat("\n=== 开始数据筛选 ===\n")
pisa_filtered <- filter_data_optimized(pisa)

# 保存筛选数据
write.csv(pisa_filtered, "filtered_data_optimized.csv", row.names = FALSE)
cat("\n已保存筛选数据到: filtered_data_optimized.csv\n")

cat("\n=== 开始模型拟合 ===\n")
gs <- fit_gam_optimized(pisa_filtered)

# 模型统计
model_summary <- summary(gs)
r_squared <- round(model_summary$r.sq, 3)
p_value <- model_summary$s.pv[1]
p_value_formatted <- ifelse(p_value < 0.001, "< 0.001", sprintf("%.4f", p_value))

cat(sprintf("\n=== 最终模型结果 ===\n"))
cat(sprintf("R² = %.3f\n", r_squared))
cat(sprintf("p = %s\n", p_value_formatted))

# 生成预测数据
gs_pred_df <- tidygam::predict_gam(gs, length_out = 300) %>%
  dplyr::mutate(
    y = estimate,
    lower_ci = conf.low,
    upper_ci = conf.high
  )

# 创建优化图形
final_plot <- ggplot() +
  geom_ribbon(
    data = gs_pred_df, 
    aes(x = x, ymin = lower_ci, ymax = upper_ci), 
    fill = "lightblue", alpha = 0.3
  ) +
  geom_point(
    data = pisa_filtered %>% filter(x <= 8), 
    aes(x = x, y = y), 
    alpha = 0.25, size = 1.0, color = "darkblue"
  ) +
  geom_point(
    data = pisa_filtered %>% filter(x > 8), 
    aes(x = x, y = y), 
    alpha = 0.3, size = 1.0, color = "purple"
  ) +
  geom_line(
    data = gs_pred_df, 
    aes(x = x, y = y), 
    color = "red", size = 1.5
  ) +
  geom_hline(yintercept = 100, color = "black", linetype = "dashed", size = 0.7) +
  annotate(
    "text", x = 2, y = 118, 
    label = paste0("R² = ", r_squared, "\np = ", p_value_formatted), 
    hjust = 0, vjust = 1, size = 4.5, color = "darkgreen"
  ) +
  coord_cartesian(ylim = c(85, 120), xlim = c(0, 20)) +
  labs(
    x = "Nebkhas short axis (m)", 
    y = "Shannon index",
    title = "优化后的广义加性模型拟合",
    subtitle = "x>8区域增强点密度（紫色点）"
  ) +
  theme_bw(base_size = 12) +
  theme(
    plot.title = element_text(hjust = 0.5, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5, color = "purple")
  )

print(final_plot)

# 保存图形
ggsave("natural_fit_optimized.tiff", plot = final_plot, width = 10, height = 6, dpi = 300)
ggsave("natural_fit_optimized.pdf", plot = final_plot, width = 10, height = 6, dpi = 600)

cat("\n=== 优化完成 ===\n")
cat("图形已保存为: natural_fit_optimized.tiff 和 natural_fit_optimized.pdf\n")
