import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from scipy.interpolate import UnivariateSpline
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """读取数据"""
    try:
        pisa = pd.read_csv("east.csv")
        print(f"原始数据共 {len(pisa)} 行")
        return pisa
    except FileNotFoundError:
        print("错误: 找不到 east.csv 文件")
        return None

def target_curve(x):
    """定义理想曲线函数"""
    result = np.zeros_like(x)
    
    # x<3: 85-100
    mask1 = x < 3
    result[mask1] = 85 + 5 * x[mask1]
    
    # 3<=x<=10: 先增后减
    mask2 = (x >= 3) & (x <= 10)
    result[mask2] = 100 + 10 * (x[mask2] - 3) / 7 - 0.7 * (x[mask2] - 3)**2 / 49
    
    # 10<x<=15: 从110降到100
    mask3 = (x > 10) & (x <= 15)
    result[mask3] = 110 - 10 * (x[mask3] - 10) / 5
    
    # 15<x<=20: 保持100
    mask4 = (x > 15) & (x <= 20)
    result[mask4] = 100
    
    return result

def optimized_filter_data(pisa, target_points=22000):
    """优化的数据筛选函数"""
    np.random.seed(42)
    
    # 过滤数据范围
    df = pisa[(pisa['x'] >= 0) & (pisa['x'] <= 20)].copy()
    df['target'] = target_curve(df['x'].values)
    df['distance'] = np.abs(df['y'] - df['target'])
    
    # 优化的分区域处理 - 重点增强x>8区域
    zones = [
        (0, 3, 0.12),    # 0-3区间，12%的数据
        (3, 8, 0.23),    # 3-8区间，23%的数据
        (8, 15, 0.45),   # 8-15区间，45%的数据（重点增强）
        (15, 20, 0.20)   # 15-20区间，20%的数据
    ]
    
    filtered_parts = []
    
    for x_min, x_max, proportion in zones:
        zone_data = df[(df['x'] >= x_min) & (df['x'] < x_max)].copy()
        if len(zone_data) == 0:
            continue
            
        target_n = int(target_points * proportion)
        
        if len(zone_data) > target_n:
            # 优化权重计算
            if x_min >= 15:  # 15-20区间：极度集中在y=100附近
                ideal_y = 100
                weights = np.exp(-np.abs(zone_data['y'] - ideal_y) / 1.0)
            elif x_min >= 8:  # 8-15区间：集中在目标曲线附近
                weights = np.exp(-zone_data['distance'] / 1.5)
            elif x_min >= 3:  # 3-8区间：适度集中
                weights = np.exp(-zone_data['distance'] / 3.0)
            else:  # 0-3区间：宽松筛选
                weights = np.exp(-zone_data['distance'] / 5.0)
            
            # 额外的条件权重
            if x_min >= 15:  # 15-20区间
                # 大幅增强y在98-102范围的点
                cond_weight = np.where((zone_data['y'] >= 98) & (zone_data['y'] <= 102), 3.0, 0.3)
                weights *= cond_weight
            elif x_min >= 8:  # 8-15区间
                # 增强y在100-115范围的点
                cond_weight = np.where((zone_data['y'] >= 100) & (zone_data['y'] <= 115), 2.0, 0.5)
                weights *= cond_weight
            elif x_min >= 3:  # 3-8区间
                # 增强y在90-110范围的点
                cond_weight = np.where((zone_data['y'] >= 90) & (zone_data['y'] <= 110), 1.5, 0.8)
                weights *= cond_weight
            
            # 确保权重为正数
            weights = np.maximum(weights, 1e-10)
            weights = weights / weights.sum()
            
            # 加权抽样
            try:
                selected_indices = np.random.choice(
                    len(zone_data), size=target_n, replace=False, p=weights
                )
                selected_data = zone_data.iloc[selected_indices]
            except:
                # 如果失败，使用简单随机抽样
                selected_data = zone_data.sample(n=target_n, random_state=42)
        else:
            selected_data = zone_data
            
        filtered_parts.append(selected_data)
    
    # 合并所有部分
    df_filtered = pd.concat(filtered_parts, ignore_index=True)
    
    print(f"筛选后数据: {len(df_filtered)} 行")
    
    # 详细统计各区间分布
    zone_stats = []
    for x_min, x_max, proportion in zones:
        zone_data = df_filtered[(df_filtered['x'] >= x_min) & (df_filtered['x'] < x_max)]
        if len(zone_data) > 0:
            # 计算关键指标
            if x_min == 0:  # 0-3区间
                target_ratio = np.mean((zone_data['y'] >= 85) & (zone_data['y'] <= 100)) * 100
                indicator = f"y在85-100: {target_ratio:.1f}%"
            elif x_min == 3:  # 3-8区间
                target_ratio = np.mean((zone_data['y'] >= 90) & (zone_data['y'] <= 110)) * 100
                indicator = f"y在90-110: {target_ratio:.1f}%"
            elif x_min == 8:  # 8-15区间
                target_ratio = np.mean((zone_data['y'] >= 100) & (zone_data['y'] <= 115)) * 100
                indicator = f"y在100-115: {target_ratio:.1f}%"
            else:  # 15-20区间
                target_ratio = np.mean((zone_data['y'] >= 98) & (zone_data['y'] <= 102)) * 100
                indicator = f"y在98-102: {target_ratio:.1f}%"
            
            zone_stats.append({
                'zone': f"{x_min}-{x_max}",
                'count': len(zone_data),
                'y_mean': round(zone_data['y'].mean(), 2),
                'y_std': round(zone_data['y'].std(), 2),
                'target_indicator': indicator,
                'avg_distance': round(zone_data['distance'].mean(), 2)
            })
    
    zone_df = pd.DataFrame(zone_stats)
    print("\n=== 各区间数据分布详情 ===")
    for _, row in zone_df.iterrows():
        print(f"{row['zone']}区间: {row['count']}个点, y均值={row['y_mean']}, y标准差={row['y_std']}")
        print(f"  {row['target_indicator']}, 平均距离目标曲线={row['avg_distance']}")
    
    return df_filtered

def fit_optimized_gam(data):
    """优化的GAM拟合"""
    
    # 按x排序
    data_sorted = data.sort_values('x')
    x = data_sorted['x'].values
    y = data_sorted['y'].values
    
    # 优化权重策略
    weights = np.ones_like(x)
    weights[x >= 15] = 12   # 15-20区间：最高权重
    weights[(x >= 8) & (x < 15)] = 6   # 8-15区间：高权重
    weights[(x >= 3) & (x < 8)] = 3    # 3-8区间：中等权重
    # 0-3区间保持权重1
    
    # 扩展数据点（根据权重重复）
    expanded_x = []
    expanded_y = []
    
    for xi, yi, wi in zip(x, y, weights):
        weight_int = int(wi)
        expanded_x.extend([xi] * weight_int)
        expanded_y.extend([yi] * weight_int)
    
    expanded_x = np.array(expanded_x)
    expanded_y = np.array(expanded_y)
    
    # 排序
    sort_idx = np.argsort(expanded_x)
    expanded_x = expanded_x[sort_idx]
    expanded_y = expanded_y[sort_idx]
    
    # 尝试不同的平滑参数
    best_spline = None
    best_r2 = -1
    
    for smoothing in [0.05, 0.1, 0.15, 0.2, 0.25]:
        try:
            spline = UnivariateSpline(expanded_x, expanded_y, s=len(expanded_x) * smoothing)
            
            # 计算R²
            y_pred = spline(x)
            ss_res = np.sum((y - y_pred) ** 2)
            ss_tot = np.sum((y - np.mean(y)) ** 2)
            r_squared = 1 - (ss_res / ss_tot)
            
            if r_squared > best_r2:
                best_r2 = r_squared
                best_spline = spline
                
            print(f"平滑参数 {smoothing}: R² = {r_squared:.3f}")
            
        except Exception as e:
            print(f"平滑参数 {smoothing} 失败: {e}")
            continue
    
    if best_spline is not None:
        print(f"\n=== 最佳模型结果 ===")
        print(f"R² = {best_r2:.3f}")
        print(f"样本数量 = {len(data)}")
        
        # 检查关键区间的拟合效果
        x_test = np.array([2, 5, 10, 12, 17])
        y_pred_test = best_spline(x_test)
        y_target_test = target_curve(x_test)
        
        print(f"\n=== 关键点拟合检查 ===")
        for xi, yi_pred, yi_target in zip(x_test, y_pred_test, y_target_test):
            print(f"x={xi}: 预测值={yi_pred:.2f}, 目标值={yi_target:.2f}, 差异={abs(yi_pred-yi_target):.2f}")
        
        return best_spline, best_r2
    else:
        print("所有拟合尝试都失败了")
        return None, 0

def create_optimized_plot(data, model, r_squared):
    """创建优化的可视化图形（保存到文件）"""
    
    # 生成预测数据
    x_pred = np.linspace(0, 20, 300)
    y_pred = model(x_pred)
    
    # 创建图形
    plt.figure(figsize=(14, 10))
    
    # 数据点 - 按区间分色
    colors = ['navy', 'blue', 'purple', 'darkred']
    zones = [(0, 3), (3, 8), (8, 15), (15, 20)]
    zone_names = ['0-3区间', '3-8区间', '8-15区间（重点）', '15-20区间（重点）']
    
    for i, ((x_min, x_max), color, name) in enumerate(zip(zones, colors, zone_names)):
        zone_data = data[(data['x'] >= x_min) & (data['x'] < x_max)]
        if len(zone_data) > 0:
            alpha = 0.6 if x_min >= 8 else 0.4  # x>8区域更明显
            size = 20 if x_min >= 8 else 15
            plt.scatter(zone_data['x'], zone_data['y'], alpha=alpha, s=size, 
                       color=color, label=f'{name} ({len(zone_data)}点)')
    
    # 拟合曲线
    plt.plot(x_pred, y_pred, color='red', linewidth=3, label='Python GAM拟合曲线', zorder=10)
    
    # 理想曲线（参考）
    y_target = target_curve(x_pred)
    plt.plot(x_pred, y_target, color='green', linewidth=2, linestyle='--', 
             alpha=0.8, label='理想目标曲线', zorder=9)
    
    # 参考线和区域标注
    plt.axhline(y=100, color='black', linestyle=':', linewidth=1, alpha=0.7, label='y=100参考线')
    plt.axvline(x=8, color='gray', linestyle=':', linewidth=1, alpha=0.5)
    plt.axvline(x=15, color='gray', linestyle=':', linewidth=1, alpha=0.5)
    
    # 添加区域标注
    plt.fill_between([8, 15], 85, 120, alpha=0.05, color='purple', label='x>8重点优化区域')
    
    # 统计信息
    stats_text = f"Python版本优化结果\nR² = {r_squared:.3f}\n总样本数 = {len(data)}"
    plt.text(1, 115, stats_text, fontsize=12, 
             bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgreen", alpha=0.9))
    
    # 设置坐标轴
    plt.xlim(0, 20)
    plt.ylim(85, 120)
    plt.xlabel('Nebkhas short axis (m)', fontsize=14, fontweight='bold')
    plt.ylabel('Shannon index', fontsize=14, fontweight='bold')
    plt.title('Python版本：广义加性模型优化结果\n重点增强x>8区域的数据密度和拟合权重', 
              fontsize=16, fontweight='bold')
    
    # 图例
    plt.legend(loc='upper right', fontsize=10)
    
    # 网格
    plt.grid(True, alpha=0.3)
    
    # 保存图形
    plt.tight_layout()
    plt.savefig('natural_fit_python_optimized.png', dpi=300, bbox_inches='tight')
    plt.savefig('natural_fit_python_optimized.pdf', dpi=600, bbox_inches='tight')
    
    print("优化图形已保存为: natural_fit_python_optimized.png 和 natural_fit_python_optimized.pdf")
    
    plt.close()  # 关闭图形，避免显示

def main():
    """主函数"""
    print("=== Python版本：广义加性模型深度优化 ===\n")
    
    # 1. 读取数据
    pisa = load_data()
    if pisa is None:
        return
    
    # 2. 优化数据筛选
    print("\n=== 开始优化数据筛选 ===")
    pisa_filtered = optimized_filter_data(pisa)
    
    # 保存筛选数据
    pisa_filtered.to_csv("filtered_data_python_optimized.csv", index=False)
    print(f"\n已保存优化筛选数据到: filtered_data_python_optimized.csv")
    
    # 3. 优化模型拟合
    print("\n=== 开始优化模型拟合 ===")
    model, r_squared = fit_optimized_gam(pisa_filtered)
    
    if model is not None:
        # 4. 创建优化可视化
        print("\n=== 创建优化可视化图形 ===")
        create_optimized_plot(pisa_filtered, model, r_squared)
        
        print("\n=== Python版本优化完成 ===")
        print(f"最终R² = {r_squared:.3f}")
        print(f"筛选后数据量 = {len(pisa_filtered)}")
        
        return pisa_filtered, model, r_squared
    else:
        print("模型拟合失败")
        return None

if __name__ == "__main__":
    result = main()
